# Next AI Agent Implementation Prompt

## 🎯 **Mission: Implement Electrical Entity**

You are continuing the implementation of the Ultimate Electrical Designer backend. The foundation is solid with 3 core entities complete, and you're implementing the final core business entity.

## 📋 **Current State Summary**

### ✅ **Completed Foundation (100%)**
1. **Project Entity** - Complete with all 5 layers + comprehensive tests
2. **Component Entity** - Complete with catalog management and validation
3. **Heat Tracing Entity** - Complete with design workflow and calculations integration
4. **Calculations Layer** - Engineering calculations (heat loss, electrical sizing)
5. **Standards Layer** - TR 50410, IEC 60079-30-1 compliance validation
6. **Model-Level Validation** - SQLAlchemy event listeners for data integrity

### 🔧 **Established Architecture Patterns**
- **5-Layer Architecture**: Model → Repository → Service → API → Tests
- **Comprehensive Error Handling**: Custom exceptions with detailed error context
- **Standards Integration**: Calculations integrate with compliance validation
- **Robust Testing**: Unit tests for each layer with mocking strategies
- **Type Safety**: Full type hints and Pydantic validation throughout

## 🎯 **Your Task: Electrical Entity Implementation**

### **Priority**: CRITICAL - Final Core Business Domain
The Electrical entity represents the electrical design workflow and integrates heavily with the calculations layer for cable sizing and voltage drop calculations.

### **Models to Implement** (Already exist in `backend/core/models/electrical.py`):
1. **ElectricalNode** - Electrical connection points (switchboards, junction boxes)
2. **CableRoute** - Cable routing between electrical nodes
3. **CableSegment** - Individual cable segments with specifications
4. **LoadCalculation** - Electrical load calculations and power requirements
5. **VoltageDropCalculation** - Voltage drop calculations for cable sizing

### **Required Implementation (Follow Established Patterns)**

#### 1. **Electrical Schemas** (`backend/core/schemas/electrical_schemas.py`)
**Create comprehensive Pydantic schemas for all electrical entities:**

- **ElectricalNodeCreateSchema/UpdateSchema/ReadSchema** - Node management with electrical properties
- **CableRouteCreateSchema/UpdateSchema/ReadSchema** - Route management with path validation
- **CableSegmentCreateSchema/UpdateSchema/ReadSchema** - Segment design with cable specifications
- **LoadCalculationCreateSchema/ReadSchema** - Load calculation input/output
- **VoltageDropCalculationCreateSchema/ReadSchema** - Voltage drop calculation input/output
- **Calculation Input/Output Schemas** - Integration with calculations layer
- **Validation Rules** - Electrical engineering constraints and safety requirements

**Key Requirements:**
- Integrate with calculations layer for cable sizing and voltage drop validation
- Validate electrical engineering constraints (voltages, currents, power ratings)
- Support complex nested relationships (routes → segments → cables)
- Include calculation result schemas for API responses
- Validate cable specifications against component catalog

#### 2. **Electrical Repositories** (`backend/core/repositories/electrical_repository.py`)
**Extend BaseRepository for each electrical model:**

- **ElectricalNodeRepository** - CRUD + project-scoped queries + electrical property filtering
- **CableRouteRepository** - CRUD + route optimization queries + path calculations
- **CableSegmentRepository** - CRUD + cable specification queries + load calculations
- **LoadCalculationRepository** - CRUD + power calculation queries + load balancing
- **VoltageDropCalculationRepository** - CRUD + voltage drop queries + cable sizing
- **Complex Queries** - Route optimization, load balancing, cable sizing queries
- **Performance Optimization** - Eager loading for related calculations and components

#### 3. **Electrical Service** (`backend/core/services/electrical_service.py`)
**Business logic layer with electrical engineering calculations integration:**

- **Design Workflow** - Complete electrical design process
- **Calculations Integration** - Use `CalculationService` for cable sizing and voltage drop calculations
- **Standards Validation** - Use `StandardsManager` for electrical compliance checking
- **Route Optimization** - Automatic cable route optimization algorithms
- **Load Balancing** - Power distribution and load balancing logic
- **Cable Sizing** - Automatic cable selection based on load and voltage drop requirements
- **Validation** - Electrical engineering constraint validation and safety checks

**Critical Integration Points:**
```python
# Example integration pattern
from backend.core.calculations.calculation_service import CalculationService, CableSizingInput
from backend.core.standards.standards_manager import StandardsManager

# In service methods:
cable_sizing_result = self.calculation_service.calculate_cable_sizing(cable_sizing_input)
validation_result = self.standards_manager.validate_electrical_design(cable_sizing_result, design_params)
```

#### 4. **Electrical API Routes** (`backend/api/v1/electrical_routes.py`)
**RESTful endpoints for electrical operations:**

- **CRUD Endpoints** - Standard create/read/update/delete for all entities
- **Design Endpoints** - Route design and optimization endpoints
- **Calculation Endpoints** - Cable sizing and voltage drop calculation endpoints
- **Validation Endpoints** - Electrical standards compliance checking endpoints
- **Complex Operations** - Bulk operations, route optimization, load balancing

#### 5. **Comprehensive Test Suite**
**Follow established testing patterns:**

- **Schema Tests** - Validation rules, calculation integration, nested relationships
- **Repository Tests** - CRUD operations, complex queries, performance optimization
- **Service Tests** - Business logic, calculations integration, standards validation
- **API Tests** - All endpoints, error scenarios, integration workflows
- **Integration Tests** - End-to-end electrical design workflows

## 🔗 **Critical Integration Requirements**

### **Calculations Layer Integration**
- Use `CalculationService` for all electrical calculations
- Integrate cable sizing results into route/segment entities
- Apply voltage drop calculations to cable selection and routing
- Handle calculation errors gracefully with proper user feedback

### **Standards Layer Integration**
- Use `StandardsManager` for all electrical compliance validation
- Apply safety factors from active electrical standards
- Validate electrical ratings for hazardous area applications
- Ensure all designs meet electrical engineering standards requirements

### **Component Integration**
- Reference electrical cables and components from Component entity
- Validate component compatibility with electrical design requirements
- Use component specifications in calculations and validation

### **Heat Tracing Integration**
- Connect electrical circuits to heat tracing circuits
- Calculate electrical loads from heat tracing power requirements
- Validate electrical capacity for heat tracing loads

## 📚 **Reference Implementation Patterns**

### **Study These Completed Examples:**
1. **Project Entity** - `backend/core/schemas/project_schemas.py` for schema patterns
2. **Component Entity** - `backend/core/services/component_service.py` for service patterns
3. **Heat Tracing Entity** - `backend/core/schemas/heat_tracing_schemas.py` for complex validation patterns
4. **Heat Tracing Repository** - `backend/core/repositories/heat_tracing_repository.py` for advanced queries
5. **Calculation Service** - `backend/core/calculations/calculation_service.py` for integration
6. **Standards Manager** - `backend/core/standards/standards_manager.py` for validation

### **Follow These Conventions:**
- Use `BaseSoftDeleteSchema` for read schemas with soft delete fields
- Implement comprehensive logging with contextual information
- Use custom exceptions from `backend.core.errors.exceptions`
- Follow established error handling and transaction management patterns
- Maintain high test coverage (>90%) with proper mocking strategies

## 🎯 **Success Criteria**

### **Functional Requirements:**
- [ ] Complete electrical design workflow from node/route input to cable sizing
- [ ] Integration with calculations layer for accurate cable sizing and voltage drop calculations
- [ ] Standards compliance validation for all electrical designs
- [ ] Comprehensive error handling and user feedback
- [ ] High-performance queries for complex electrical operations
- [ ] Integration with heat tracing loads for electrical capacity planning

### **Quality Requirements:**
- [ ] >90% test coverage across all layers
- [ ] Comprehensive type hints and documentation
- [ ] Proper error handling with detailed error messages
- [ ] Performance optimization for complex calculations
- [ ] Integration tests demonstrating end-to-end workflows

### **Integration Requirements:**
- [ ] Seamless integration with calculations layer
- [ ] Standards validation for all electrical designs
- [ ] Component compatibility validation
- [ ] Project-scoped operations with proper access control
- [ ] Heat tracing load integration for electrical planning

## 🚀 **Getting Started**

1. **Review the existing models** in `backend/core/models/electrical.py`
2. **Study the calculation examples** - Run the working cable sizing calculations
3. **Examine the standards validation** - Test the working standards manager
4. **Follow the established patterns** from Project, Component, and Heat Tracing entities
5. **Start with schemas** - Build the foundation with comprehensive validation
6. **Integrate early and often** - Test calculations and standards integration continuously

## 📖 **Key Documentation References**

- `backend/docs/core/calculations/calculations-architecture.md` - Calculations integration guide
- `backend/docs/core/standards/standards-architecture.md` - Standards validation guide
- `backend/docs/core/models/validation-architecture.md` - Model validation patterns
- `backend/docs/implementation-progress.md` - Current progress and patterns
- `backend/docs/project-entity-completion-summary.md` - Established conventions
- `backend/docs/heat-tracing-implementation-summary.md` - Recent implementation example

**Remember**: You're building on a solid, tested foundation with 3 complete entities as examples. The calculations and standards layers are working and ready for integration. Focus on creating a seamless electrical design experience that leverages all the architectural components and integrates with the heat tracing loads! ⚡
