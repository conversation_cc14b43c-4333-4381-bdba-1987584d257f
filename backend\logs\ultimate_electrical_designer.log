2025-05-30 11:35:41 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:35:41 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:35:41 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:35:41 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:35:41 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:36:53 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: NON-EXISTENT in project 1 (heat_tracing_repository.py:123)
2025-05-30 11:36:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - ERROR - Database error searching for pipe line tag NON-EXISTENT: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[User(User)]'. Original exception was: When initializing mapper Mapper[User(User)], expression 'ActivityLog' failed to locate a name ('ActivityLog'). If this is a class name, consider adding this relationship() to the <class 'core.models.users.User'> class after both dependent classes have been defined. (heat_tracing_repository.py:145)
2025-05-30 11:36:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 999 (heat_tracing_repository.py:242)
2025-05-30 11:36:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:36:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - ERROR - Database error counting pipes for project 1: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[User(User)]'. Original exception was: When initializing mapper Mapper[User(User)], expression 'ActivityLog' failed to locate a name ('ActivityLog'). If this is a class name, consider adding this relationship() to the <class 'core.models.users.User'> class after both dependent classes have been defined. (heat_tracing_repository.py:300)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - ERROR - Database error counting pipes for project 1: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[User(User)]'. Original exception was: When initializing mapper Mapper[User(User)], expression 'ActivityLog' failed to locate a name ('ActivityLog'). If this is a class name, consider adding this relationship() to the <class 'core.models.users.User'> class after both dependent classes have been defined. (heat_tracing_repository.py:300)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - ERROR - Error generating project summary for 1: An unexpected database error occurred during pipe operation. (heat_tracing_repository.py:1056)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Checking design readiness for project 1 (heat_tracing_repository.py:1072)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - ERROR - Database error counting pipes for project 1: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[User(User)]'. Original exception was: When initializing mapper Mapper[User(User)], expression 'ActivityLog' failed to locate a name ('ActivityLog'). If this is a class name, consider adding this relationship() to the <class 'core.models.users.User'> class after both dependent classes have been defined. (heat_tracing_repository.py:300)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - ERROR - Error generating project summary for 1: An unexpected database error occurred during pipe operation. (heat_tracing_repository.py:1056)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - ERROR - Error checking design readiness for project 1: An unexpected database error occurred during pipe operation. (heat_tracing_repository.py:1120)
2025-05-30 11:37:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:37:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:37:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:37:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:37:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:37:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:38:36 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:39:06 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:39:50 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:41:06 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:47 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes for project 1: skip=0, limit=100 (heat_tracing_repository.py:80)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 2 pipes for project 1 (heat_tracing_repository.py:99)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: L-001 in project 1 (heat_tracing_repository.py:123)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found pipe: 'Test Pipe 001' (ID: 1) (heat_tracing_repository.py:138)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: NON-EXISTENT in project 1 (heat_tracing_repository.py:123)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe not found with line tag: NON-EXISTENT (heat_tracing_repository.py:140)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 999 (heat_tracing_repository.py:242)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 999 not found for heat loss update (heat_tracing_repository.py:248)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 1 (heat_tracing_repository.py:296)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for vessel with equipment tag: T-001 in project 1 (heat_tracing_repository.py:385)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found vessel: 'Test Vessel T-001' (ID: 1) (heat_tracing_repository.py:400)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for vessel 1 (heat_tracing_repository.py:467)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Vessel 1 heat loss calculation updated successfully (heat_tracing_repository.py:489)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving HT circuits for feeder 1: skip=0, limit=100 (heat_tracing_repository.py:569)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 HT circuits for feeder 1 (heat_tracing_repository.py:588)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for HT circuit for pipe 1 (heat_tracing_repository.py:611)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found HT circuit: 'HTC-001-A' (ID: 1) (heat_tracing_repository.py:620)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Calculating total load for feeder 1 (heat_tracing_repository.py:776)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total load for feeder 1: 1.25 kW (heat_tracing_repository.py:789)
2025-05-30 11:43:24 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:43:25 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:43:25 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:43:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:43:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:43:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:21 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:21 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:21 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits for switchboard 1: skip=0, limit=100 (heat_tracing_repository.py:833)
2025-05-30 11:44:21 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 control circuits for switchboard 1 (heat_tracing_repository.py:852)
2025-05-30 11:44:21 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:21 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits with limiting function for switchboard 1 (heat_tracing_repository.py:926)
2025-05-30 11:44:21 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 control circuits with limiting function (heat_tracing_repository.py:942)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Checking design readiness for project 1 (heat_tracing_repository.py:1072)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Design readiness for project 1: 0.0% (heat_tracing_repository.py:1114)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes for project 1: skip=0, limit=100 (heat_tracing_repository.py:80)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 2 pipes for project 1 (heat_tracing_repository.py:99)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: L-001 in project 1 (heat_tracing_repository.py:123)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found pipe: 'Test Pipe 001' (ID: 1) (heat_tracing_repository.py:138)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: NON-EXISTENT in project 1 (heat_tracing_repository.py:123)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe not found with line tag: NON-EXISTENT (heat_tracing_repository.py:140)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 999 (heat_tracing_repository.py:242)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 999 not found for heat loss update (heat_tracing_repository.py:248)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 1 (heat_tracing_repository.py:296)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for vessel with equipment tag: T-001 in project 1 (heat_tracing_repository.py:385)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found vessel: 'Test Vessel T-001' (ID: 1) (heat_tracing_repository.py:400)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for vessel 1 (heat_tracing_repository.py:467)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Vessel 1 heat loss calculation updated successfully (heat_tracing_repository.py:489)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving HT circuits for feeder 1: skip=0, limit=100 (heat_tracing_repository.py:569)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 HT circuits for feeder 1 (heat_tracing_repository.py:588)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for HT circuit for pipe 1 (heat_tracing_repository.py:611)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found HT circuit: 'HTC-001-A' (ID: 1) (heat_tracing_repository.py:620)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Calculating total load for feeder 1 (heat_tracing_repository.py:776)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total load for feeder 1: 1.25 kW (heat_tracing_repository.py:789)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits for switchboard 1: skip=0, limit=100 (heat_tracing_repository.py:833)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 control circuits for switchboard 1 (heat_tracing_repository.py:852)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits with limiting function for switchboard 1 (heat_tracing_repository.py:926)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 control circuits with limiting function (heat_tracing_repository.py:942)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Checking design readiness for project 1 (heat_tracing_repository.py:1072)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Design readiness for project 1: 0.0% (heat_tracing_repository.py:1114)
2025-05-30 11:45:14 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes for project 1: skip=0, limit=100 (heat_tracing_repository.py:80)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 2 pipes for project 1 (heat_tracing_repository.py:99)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: L-001 in project 1 (heat_tracing_repository.py:123)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found pipe: 'Test Pipe 001' (ID: 1) (heat_tracing_repository.py:138)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: NON-EXISTENT in project 1 (heat_tracing_repository.py:123)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe not found with line tag: NON-EXISTENT (heat_tracing_repository.py:140)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 999 (heat_tracing_repository.py:242)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 999 not found for heat loss update (heat_tracing_repository.py:248)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 1 (heat_tracing_repository.py:296)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for vessel with equipment tag: T-001 in project 1 (heat_tracing_repository.py:385)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found vessel: 'Test Vessel T-001' (ID: 1) (heat_tracing_repository.py:400)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for vessel 1 (heat_tracing_repository.py:467)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Vessel 1 heat loss calculation updated successfully (heat_tracing_repository.py:489)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving HT circuits for feeder 1: skip=0, limit=100 (heat_tracing_repository.py:569)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 HT circuits for feeder 1 (heat_tracing_repository.py:588)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for HT circuit for pipe 1 (heat_tracing_repository.py:611)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found HT circuit: 'HTC-001-A' (ID: 1) (heat_tracing_repository.py:620)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Calculating total load for feeder 1 (heat_tracing_repository.py:776)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total load for feeder 1: 1.25 kW (heat_tracing_repository.py:789)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits for switchboard 1: skip=0, limit=100 (heat_tracing_repository.py:833)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 control circuits for switchboard 1 (heat_tracing_repository.py:852)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits with limiting function for switchboard 1 (heat_tracing_repository.py:926)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 control circuits with limiting function (heat_tracing_repository.py:942)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Checking design readiness for project 1 (heat_tracing_repository.py:1072)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Design readiness for project 1: 0.0% (heat_tracing_repository.py:1114)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes for project 1: skip=0, limit=100 (electrical_repository.py:82)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieved 1 electrical nodes for project 1 (electrical_repository.py:101)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes for project 1: skip=0, limit=100 (electrical_repository.py:82)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - ERROR - Database error retrieving electrical nodes for project 1: Database error (electrical_repository.py:107)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Searching for electrical nodes with type: SWITCHBOARD_INCOMING in project 1 (electrical_repository.py:131)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 electrical nodes with type: SWITCHBOARD_INCOMING (electrical_repository.py:151)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes with capacity >= 50.0kVA for project 1 (electrical_repository.py:179)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 electrical nodes with sufficient capacity (electrical_repository.py:198)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Counting electrical nodes for project 1 (electrical_repository.py:265)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Total electrical nodes in project 1: 5 (electrical_repository.py:275)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Counting electrical nodes for project 1 (electrical_repository.py:265)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Total electrical nodes in project 1: None (electrical_repository.py:275)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:302)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving both cable routes for node 1 (electrical_repository.py:372)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 both cable routes for node 1 (electrical_repository.py:391)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:302)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:302)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving cable routes with installation method: CABLE_TRAY for project 1 (electrical_repository.py:419)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 cable routes with installation method: CABLE_TRAY (electrical_repository.py:437)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:302)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving cable routes with voltage drop > 3.0% for project 1 (electrical_repository.py:465)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 cable routes with high voltage drop (electrical_repository.py:488)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:743)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving load calculations for electrical node 2: skip=0, limit=100 (electrical_repository.py:810)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieved 1 load calculations for electrical node 2 (electrical_repository.py:829)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:743)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving load calculations with type: heat_tracing for project 1 (electrical_repository.py:859)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 load calculations with type: heat_tracing (electrical_repository.py:879)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:743)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Calculating total power for electrical node 2 (electrical_repository.py:904)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Total power for electrical node 2: 5.5kW (electrical_repository.py:919)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:743)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Calculating total power for electrical node 2 (electrical_repository.py:904)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Total power for electrical node 2: 0.0kW (electrical_repository.py:919)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:948)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving voltage drop calculations for cable route 1: skip=0, limit=100 (electrical_repository.py:1015)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieved 1 voltage drop calculations for cable route 1 (electrical_repository.py:1034)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:948)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving non-compliant voltage drop calculations for project 1 (electrical_repository.py:1063)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 non-compliant voltage drop calculations for project 1 (electrical_repository.py:1083)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:948)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Calculating average voltage drop for project 1 (electrical_repository.py:1158)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Average voltage drop for project 1: 3.2% (electrical_repository.py:1171)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:948)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Counting compliant voltage drop calculations for project 1 (electrical_repository.py:1197)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Compliant voltage drop calculations in project 1: 8 (electrical_repository.py:1212)
