## **Data Fetching & Caching**

### **Enhancing API Client & Query Hooks Abstraction**

Beyond just generated API clients, we will create a layer of generic CRUD hooks and standardize API response handling.

* **Standardized CRUD Query/Mutation Hooks:**
  * **How:** Define generic hooks like useCrudQuery and useCrudMutation that take an API client method (e.g., projectsApi.getProject, tasksApi.createTask) and handle common patterns such as ID parameters, default cache invalidation logic, and generic error handling.
  * **Benefit:** Reduces repetitive useQuery/useMutation boilerplate for simple CRUD operations, especially for applications with many entities.
* **API Response Normalization:**
  * **How:** Implement a utility or middleware layer (perhaps in ApiService or utils/api.ts) that normalizes API responses, particularly for lists or nested data, into a consistent format (e.g., using a library like normalizr if complex nesting requires it, or simpler mapping functions).
  * **Benefit:** Components don't need to duplicate logic for parsing or reshaping data received from the backend, leading to cleaner component code and improved consistency.

### **API Client Generation**

* **Tool:** OpenAPI Generator (or similar) will be used to generate a type-safe API client from the backend's OpenAPI specification.
* **Location:** Generated client will reside in src/types/api.d.ts (for types) and src/utils/api.ts (for the client instance).
* **Benefits:**
  * Ensures type safety between frontend and backend.
  * Reduces manual API client code.
  * Keeps API definitions synchronized.

### **Data Invalidation & Revalidation**

* **React Query Invalidation:** Utilize queryClient.invalidateQueries to mark stale data and trigger re-fetches after mutations or specific events.
* **Stale-While-Revalidate (SWR):** React Query's default behavior, providing a balance between fresh data and immediate UI responsiveness.
